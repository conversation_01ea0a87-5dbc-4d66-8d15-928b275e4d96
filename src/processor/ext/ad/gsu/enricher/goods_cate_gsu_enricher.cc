#include "dragon/src/processor/ext/merchant/enricher/merchant_gsu_cate2cate_good_click_enricher.h"

#include <iostream>
#include <algorithm>
#include <unordered_map>
#include <sstream>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/client/rpc_client.h"
#include "teams/reco-arch/colossus/client/snack_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool MerchantGsuCate2CateGoodClickEnricher::InitProcessor() {
  category_list_ = config()->GetString("category_list", "");
  if (category_list_.empty()) {
    LOG(ERROR) << "miss category_list sequence";
    return false;
  }
  limit_num_ = config()->GetInt("limit_num_attr", 50);
  cate1_match_attr_ = config()->GetBoolean("cate1_match_attr", true);
  cate2_match_attr_ = config()->GetBoolean("cate2_match_attr", true);
  cate3_match_attr_ = config()->GetBoolean("cate3_match_attr", true);
  cate1_attr_ = config()->GetString("cate1_attr", "iCate1Id");
  cate2_attr_ = config()->GetString("cate2_attr", "iCate2Id");
  cate3_attr_ = config()->GetString("cate3_attr", "iCate3Id");

  // 读取特征名列表配置
  feature_names_ = config()->GetStringList("sideinfo_attrs");
  output_feature_names_ = config()->GetStringList("output_sideinfo_attrs");

  if (feature_names_.size() == 0) {
    LOG(ERROR) << "miss sideinfo_attrs";
    return false;
  }
  if (feature_names_.size() != output_feature_names_.size()) {
    LOG(ERROR) << "output_sideinfo_attrs size not equal to sideinfo_attrs size";
    return false;
  }
  // 读取
  return true;
}

int MerchantGsuCate2CateGoodClickEnricher::AddElement(
    int cnt,
    uint64 target_cate,
    bool cate_match_attr,
    const std::unordered_map<uint64, std::vector<int>> &cate_index,
    std::unordered_set<uint64> *item_id_set,
    std::vector<std::vector<int64>*> *feature_lists) {
  if (cnt < limit_num_ && target_cate > 0 && cate_match_attr) {
    auto find_it = cate_index.find(target_cate);
    if (find_it == cate_index.end()) {
      return cnt;
    }

    for (auto offset : cate_index.at(target_cate)) {
      auto *item_ptr = static_cast<const colossus::GoodClickItemV2T *>(items_ptr->first) + offset;
      if (item_id_set->find(item_ptr->item_id) != item_id_set->end()) continue;
      item_id_set->insert(item_ptr->item_id);

      // 使用配置化的特征提取器
      for (size_t i = 0; i < feature_extractors_.size(); ++i) {
        const auto& extractor = feature_extractors_[i];
        int64 value = 0;

        if (feature_names_[i] == "click_lag") {
          value = (filter_time - item_ptr->click_timestamp) / (3600 * 24);
        } else if (feature_names_[i] == "click_index") {
          value = cnt;
        } else if (extractor.extract_func) {
          value = extractor.extract_func(item_ptr);
        }

        (*feature_lists)[i]->emplace_back(value);
      }

      ++cnt;
      if (cnt >= limit_num_) break;
    }
  }

  return cnt;
}

void MerchantGsuCate2CateGoodClickEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  // 获取 feature_names_ 中所有特征，并且验证长度均相同，与cate1相同
  std::vector<std::vector<int64>*> all_feature_lists_;
  all_feature_lists_.clear();
  auto category_list = context->GetIntListCommonAttr(category_list_);
  if (!category_list || category_list->size() == 0) {
    LOG(ERROR) << "category_list size == 0";
    return;
  }
  for (auto& feature_name : feature_names_) {
    auto single_feature_list = context->GetIntListCommonAttr(feature_name);
    if (!single_feature_list) {
      LOG(ERROR) << "feature_name:" << feature_name << " is null";
      return;
    }
    if (single_feature_list->size() != category_list->size()) {
      LOG(ERROR) << "feature_name:" << feature_name << " size:" << single_feature_list->size()
                  << " not equal to category_list size:" << category_list->size();
      return;
    }
    // 将single_feature_list 添加到 all_feature_lists 这个 vector 容器中
    all_feature_lists_.emplace_back(single_feature_list);
  }
  timer_.AppendCostMs("get_all_feature_lists");

  // for candidate photos, cate1 id -> unique candidate photo indexes, keys do not contain cateId
  thread_local std::unordered_map<uint64, std::vector<int>> cate1_index;
  cate1_index.clear();
  // for candidate photos, cate2 id -> unique candidate photo indexes, keys do not contain cateId
  thread_local std::unordered_map<uint64, std::vector<int>> cate2_index;
  cate2_index.clear();
  // for candidate photos, cate3 id -> unique candidate photo indexes, keys do not contain cateId
  thread_local std::unordered_map<uint64, std::vector<int>> cate3_index;
  cate3_index.clear();
  // all target cate1 id
  thread_local std::unordered_set<uint64> target_cate1_ids;
  target_cate1_ids.clear();
  // all target cate2 id
  thread_local std::unordered_set<uint64> target_cate2_ids;
  target_cate2_ids.clear();
  // all target cate1 id
  thread_local std::unordered_set<uint64> target_cate3_ids;
  target_cate3_ids.clear();

  // generate target_cate1_ids/ target_cate2_ids / target_cate3_ids from reco_result
  auto item_cate1_attr_accessor = context->GetItemAttrAccessor(cate1_attr_);
  auto item_cate2_attr_accessor = context->GetItemAttrAccessor(cate2_attr_);
  auto item_cate3_attr_accessor = context->GetItemAttrAccessor(cate3_attr_);
  for (auto it = begin; it != end; ++it) {
    uint64 target_cate1_id = 0;
    uint64 target_cate2_id = 0;
    uint64 target_cate3_id = 0;
    if (context->HasItemAttr(*it, item_cate1_attr_accessor)) {
      auto cate1_id = context->GetIntItemAttr(*it, item_cate1_attr_accessor);
      target_cate1_id = (uint64_t)(*cate1_id);
    }
    if (context->HasItemAttr(*it, item_cate2_attr_accessor)) {
      auto cate2_id = context->GetIntItemAttr(*it, item_cate2_attr_accessor);
      target_cate2_id = (uint64_t)(*cate2_id);
    }
    if (context->HasItemAttr(*it, item_cate3_attr_accessor)) {
      auto cate3_id = context->GetIntItemAttr(*it, item_cate3_attr_accessor);
      target_cate3_id = (uint64_t)(*cate3_id);
    }
    if (target_cate1_id > 0) target_cate1_ids.insert(target_cate1_id);
    if (target_cate2_id > 0) target_cate2_ids.insert(target_cate2_id);
    if (target_cate3_id > 0) target_cate3_ids.insert(target_cate3_id);
    CL_LOG_EVERY_N(INFO, 10000) << "item key:" << it->item_key
                          << ", item_cate1_id:" << target_cate1_id
                          << ", item_cate2_id:" << target_cate2_id
                          << ", item_cate3_id:" << target_cate3_id;
  }
  if (target_cate1_ids.size() <= 0 && target_cate2_ids.size() <= 0 && target_cate3_ids.size() <= 0) {
      CL_LOG(WARNING) << "request no cate id: ";
      return;
  }
  timer_.AppendCostMs("fetch_cate_id");

  for (int i = 0; i < category_list->size(); ++i) {
    uint64 category = category_list[i];
    uint64 cate1 = (category >> 48) & 0xffff;
    uint64 cate2 = (category >> 32) & 0xffff;
    uint64 cate3 = (category >> 16) & 0xffff;
    if (cate3 > 0 && target_cate3_ids.find(cate3) != target_cate3_ids.end()) {
      cate3_index[cate3].emplace_back(i);
    }
    if (cate2 > 0 && target_cate2_ids.find(cate2) != target_cate2_ids.end()) {
      cate2_index[cate2].emplace_back(i);
    }
    if (cate1 > 0 && target_cate1_ids.find(cate1) != target_cate1_ids.end()) {
      cate1_index[cate1].emplace_back(i);
    }
  }
  timer_.AppendCostMs("build cate map from category_list");

  thread_local std::unordered_set<uint64> item_id_set;
  item_id_set.clear();

  for (auto it = begin; it != end; ++it) {
    // init target_item category
    uint64 target_cate1 = 0;
    uint64 target_cate2 = 0;
    uint64 target_cate3 = 0;
    if (context->HasItemAttr(*it, item_cate1_attr_accessor)) {
      auto cate1_id = context->GetIntItemAttr(*it, item_cate1_attr_accessor);
      target_cate1 = (uint64_t)(*cate1_id);
    }
    if (context->HasItemAttr(*it, item_cate2_attr_accessor)) {
      auto cate2_id = context->GetIntItemAttr(*it, item_cate2_attr_accessor);
      target_cate2 = (uint64_t)(*cate2_id);
    }
    if (context->HasItemAttr(*it, item_cate3_attr_accessor)) {
      auto cate3_id = context->GetIntItemAttr(*it, item_cate3_attr_accessor);
      target_cate3 = (uint64_t)(*cate3_id);
    }

    // generate gsu items for every_target_item
    item_id_set.clear();
    int cnt = 0;

    cnt = AddElement(items_ptr, cnt, target_cate3, cate3_match_attr_, cate3_index,
                &item_id_set, &feature_list_ptrs);
    cnt = AddElement(items_ptr, cnt, target_cate2, cate2_match_attr_, cate2_index,
                &item_id_set, &feature_list_ptrs);
    cnt = AddElement(items_ptr, cnt, target_cate1, cate1_match_attr_, cate1_index,
                 &item_id_set, &feature_list_ptrs);

    if (!feature_lists.empty() && !feature_lists[0].empty()) {
      uint64 item_key = it->item_key;
      // 动态设置特征属性
      for (size_t i = 0; i < feature_extractors_.size(); ++i) {
        context->SetIntListItemAttr(item_key, feature_extractors_[i].attr_name, std::move(feature_lists[i]));
      }
    }
    LOG_EVERY_N(INFO, 10000) << ", target_cate3:" << target_cate3
                             << ", target_cate2:" << target_cate2
                             << ", target_cate1:" << target_cate1
                             << ", cnt:" << cnt;
  }

  timer_.AppendCostMs("cate_search");

  CL_LOG(INFO) << ", limit_num: " << limit_num_ << ", filter_time: " << filter_time
               << ", timer: " << timer_.display() << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantGsuCate2CateGoodClickEnricher,
  MerchantGsuCate2CateGoodClickEnricher);

}  // namespace platform
}  // namespace ks

