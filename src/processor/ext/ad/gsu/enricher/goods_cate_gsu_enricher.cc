#include "dragon/src/processor/ext/ad/gsu/enricher/goods_cate_gsu_enricher.h"

#include <iostream>
#include <algorithm>
#include <unordered_map>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/client/rpc_client.h"
#include "teams/reco-arch/colossus/client/snack_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool GoodsCateGsuEnricher::InitProcessor() {
  category_list_attr_ = config()->GetString("category_list", "");
  if (category_list_attr_.empty()) {
    LOG(ERROR) << "miss category_list";
    return false;
  }
  limit_num_ = config()->GetInt("limit_num", 50);
  cate1_match_attr_ = config()->GetBoolean("cate1_match_attr", true);
  cate2_match_attr_ = config()->GetBoolean("cate2_match_attr", true);
  cate3_match_attr_ = config()->GetBoolean("cate3_match_attr", true);
  cate1_attr_ = config()->GetString("cate1_attr", "iCate1Id");
  cate2_attr_ = config()->GetString("cate2_attr", "iCate2Id");
  cate3_attr_ = config()->GetString("cate3_attr", "iCate3Id");

  // 读取 sideinfo 属性名列表
  sideinfo_attrs_ = config()->GetStringList("sideinfo_attrs");
  output_sideinfo_attrs_ = config()->GetStringList("output_sideinfo_attrs");

  if (sideinfo_attrs_.size() == 0) {
    LOG(ERROR) << "miss sideinfo_attrs";
    return false;
  }
  if (sideinfo_attrs_.size() != output_sideinfo_attrs_.size()) {
    LOG(ERROR) << "output_sideinfo_attrs size not equal to sideinfo_attrs size";
    return false;
  }

  return true;
}

void GoodsCateGsuEnricher::GetCate2IndexMap(const std::vector<int64> &category_list,
                                           std::unordered_map<int64, std::vector<int>> *cate1_index,
                                           std::unordered_map<int64, std::vector<int>> *cate2_index,
                                           std::unordered_map<int64, std::vector<int>> *cate3_index) {
  for (int i = 0; i < category_list.size(); ++i) {
    int64 category = category_list[i];
    int64 cate1 = (category >> 48) & 0xffff;
    int64 cate2 = (category >> 32) & 0xffff;
    int64 cate3 = (category >> 16) & 0xffff;

    if (cate1 > 0) {
      (*cate1_index)[cate1].emplace_back(i);
    }
    if (cate2 > 0) {
      (*cate2_index)[cate2].emplace_back(i);
    }
    if (cate3 > 0) {
      (*cate3_index)[cate3].emplace_back(i);
    }
  }
}

void GoodsCateGsuEnricher::AddElementsByCategory(int64 target_cate,
                                                bool cate_match_attr,
                                                const std::unordered_map<int64, std::vector<int>> &cate_index,
                                                const std::vector<std::vector<int64>*> &all_sideinfo_lists,
                                                std::vector<std::vector<int64>> *output_lists,
                                                int *cnt) {
  if (*cnt >= limit_num_ || target_cate <= 0 || !cate_match_attr) {
    return;
  }

  auto find_it = cate_index.find(target_cate);
  if (find_it == cate_index.end()) {
    return;
  }

  for (auto index : find_it->second) {
    if (*cnt >= limit_num_) break;

    // 从每个 sideinfo 属性列表中提取对应 index 的元素
    for (size_t i = 0; i < all_sideinfo_lists.size(); ++i) {
      if (index >= all_sideinfo_lists[i]->size()) {
        LOG(WARNING) << "index:" << index << " out of range, all_sideinfo_lists[" << i << "].size():" << all_sideinfo_lists[i]->size();
      } else {
        (*output_lists)[i].emplace_back((*all_sideinfo_lists[i])[index]);
      }
    }

    (*cnt)++;
  }
}

void GoodsCateGsuEnricher::Enrich(MutableRecoContextInterface *context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();

  // 获取 category_list 和所有 sideinfo 属性
  auto category_list = context->GetIntListCommonAttr(category_list_attr_);
  if (!category_list || category_list->size() == 0) {
    LOG(ERROR) << "category_list size == 0";
    return;
  }

  // 获取所有 sideinfo 属性列表并验证长度
  std::vector<std::vector<int64>*> all_sideinfo_lists;
  for (const auto& attr_name : sideinfo_attrs_) {
    auto attr_list = context->GetIntListCommonAttr(attr_name);
    if (!attr_list) {
      LOG(ERROR) << "sideinfo attr:" << attr_name << " is null";
      return;
    }
    if (attr_list->size() != category_list->size()) {
      LOG(ERROR) << "sideinfo attr:" << attr_name << " size:" << attr_list->size()
                  << " not equal to category_list size:" << category_list->size();
      return;
    }
    all_sideinfo_lists.emplace_back(const_cast<std::vector<int64>*>(attr_list));
  }
  timer_.AppendCostMs("get_sideinfo_lists");

  // 构建 category 到 index 的映射
  thread_local std::unordered_map<int64, std::vector<int>> cate1_index;
  thread_local std::unordered_map<int64, std::vector<int>> cate2_index;
  thread_local std::unordered_map<int64, std::vector<int>> cate3_index;
  cate1_index.clear();
  cate2_index.clear();
  cate3_index.clear();

  GetCate2IndexMap(*category_list, &cate1_index, &cate2_index, &cate3_index);
  timer_.AppendCostMs("build_cate_index_map");

  // 获取 item 的 category 属性访问器
  auto item_cate1_attr_accessor = context->GetItemAttrAccessor(cate1_attr_);
  auto item_cate2_attr_accessor = context->GetItemAttrAccessor(cate2_attr_);
  auto item_cate3_attr_accessor = context->GetItemAttrAccessor(cate3_attr_);

  // 为每个 item 生成 GSU 特征
  for (auto it = begin; it != end; ++it) {
    // 获取目标 item 的 category
    int64 target_cate1 = 0;
    int64 target_cate2 = 0;
    int64 target_cate3 = 0;
    if (context->HasItemAttr(*it, item_cate1_attr_accessor)) {
      auto cate1_id = context->GetIntItemAttr(*it, item_cate1_attr_accessor);
      target_cate1 = (int64_t)(*cate1_id);
    }
    if (context->HasItemAttr(*it, item_cate2_attr_accessor)) {
      auto cate2_id = context->GetIntItemAttr(*it, item_cate2_attr_accessor);
      target_cate2 = (int64_t)(*cate2_id);
    }
    if (context->HasItemAttr(*it, item_cate3_attr_accessor)) {
      auto cate3_id = context->GetIntItemAttr(*it, item_cate3_attr_accessor);
      target_cate3 = (int64_t)(*cate3_id);
    }

    // 为每个 sideinfo 属性创建输出列表
    std::vector<std::vector<int64>> output_lists(sideinfo_attrs_.size());
    int cnt = 0;

    // 按优先级添加元素：cate3 -> cate2 -> cate1
    AddElementsByCategory(target_cate3, cate3_match_attr_, cate3_index, all_sideinfo_lists, &output_lists, &cnt);
    AddElementsByCategory(target_cate2, cate2_match_attr_, cate2_index, all_sideinfo_lists, &output_lists, &cnt);
    AddElementsByCategory(target_cate1, cate1_match_attr_, cate1_index, all_sideinfo_lists, &output_lists, &cnt);

    // 设置 item 属性
    if (cnt > 0) {
      uint64 item_key = it->item_key;
      for (size_t i = 0; i < output_sideinfo_attrs_.size(); ++i) {
        context->SetIntListItemAttr(item_key, output_sideinfo_attrs_[i], std::move(output_lists[i]));
      }
    }

    LOG_EVERY_N(INFO, 10000) << "target_cate3:" << target_cate3
                             << ", target_cate2:" << target_cate2
                             << ", target_cate1:" << target_cate1
                             << ", cnt:" << cnt;
  }

  timer_.AppendCostMs("generated_gsu_features");
  CL_LOG(INFO) << "limit_num: " << limit_num_
               << ", timer: " << timer_.display() << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GoodsCateGsuEnricher, GoodsCateGsuEnricher);

}  // namespace platform
}  // namespace ks

