#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <functional>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

// 特征提取器结构体
struct FeatureExtractor {
  std::string attr_name;  // 输出属性名
  std::function<int64(const colossus::GoodClickItemV2T*)> extract_func;  // 提取函数(FlatBuffer)
  std::function<int64(const colossus::GoodClickItemV2*)> extract_func_pb;  // 提取函数(Protobuf)
};

class MerchantGsuCate2CateGoodClickEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantGsuCate2CateGoodClickEnricher() {}
  ~MerchantGsuCate2CateGoodClickEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  int AddElement(
    const std::pair<const void *, int> *items_ptr,
    int64 filter_time,
    int cnt,
    uint64 target_cate,
    bool cate_match_attr,
    const std::unordered_map<uint64, std::vector<int>> &cate_index,
    std::unordered_set<uint64> *item_id_set,
    std::vector<std::vector<int64>*> *feature_lists);
  int AddElementPb(
    const std::vector<const ::google::protobuf::Message *> *items_ptr,
    int64 filter_time,
    int cnt,
    uint64 target_cate,
    bool cate_match_attr,
    const std::unordered_map<uint64, std::vector<int>> &cate_index,
    std::unordered_set<uint64> *item_id_set,
    std::vector<std::vector<int64>*> *feature_lists);

  void InitFeatureExtractors();

 private:
  std::string colossus_resp_attr_;
  bool filter_future_attr_ = true;
  bool if_parse_to_pb_attr_ = true;
  int limit_num_ = 50;
  std::string cate1_attr_;
  std::string cate2_attr_;
  std::string cate3_attr_;
  bool cate1_match_attr_ = true;
  bool cate2_match_attr_ = true;
  bool cate3_match_attr_ = true;

  // 配置化的特征列表
  std::vector<std::string> feature_names_;  // 从配置读取的特征名列表
  std::vector<FeatureExtractor> feature_extractors_;  // 特征提取器列表

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(MerchantGsuCate2CateGoodClickEnricher);
};

}  // namespace platform
}  // namespace ks
