#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

class GoodsCateGsuEnricher : public CommonRecoBaseEnricher {
 public:
  GoodsCateGsuEnricher() {}
  ~GoodsCateGsuEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

  void GetCate2IndexMap(const std::vector<int64> &category_list,
                        std::unordered_map<int64, std::vector<int>> *cate1_index,
                        std::unordered_map<int64, std::vector<int>> *cate2_index,
                        std::unordered_map<int64, std::vector<int>> *cate3_index);

  void AddElementsByCategory(int64 target_cate,
                            bool cate_match_attr,
                            const std::unordered_map<int64, std::vector<int>> &cate_index,
                            const std::vector<std::vector<int64>*> &all_sideinfo_lists,
                            std::vector<std::vector<int64>> *output_lists,
                            int *cnt);

 private:
  std::string category_list_attr_;
  int limit_num_ = 50;
  std::string cate1_attr_;
  std::string cate2_attr_;
  std::string cate3_attr_;
  bool cate1_match_attr_ = true;
  bool cate2_match_attr_ = true;
  bool cate3_match_attr_ = true;

  // sideinfo 属性配置
  std::vector<std::string> sideinfo_attrs_;        // 输入的 sideinfo 属性名列表
  std::vector<std::string> output_sideinfo_attrs_; // 输出的 sideinfo 属性名列表

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(GoodsCateGsuEnricher);
};

}  // namespace platform
}  // namespace ks
